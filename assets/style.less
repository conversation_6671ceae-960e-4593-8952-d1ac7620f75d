/*------- normalize -------*/
*{margin: 0; padding: 0; border: 0; outline: none; -webkit-tap-highlight-color: transparent; box-sizing: border-box; -webkit-font-smoothing: antialiased; -webkit-backface-visibility: hidden;}
img{max-width: 100%; height: auto;}
html{font-family: sans-serif; /* 1 */ -ms-text-size-adjust: 100%; /* 2 */ -webkit-text-size-adjust: 100%; /* 2 */ /*scroll-behavior: smooth;*/}
article, aside, details, figcaption, figure, footer, header, hgroup, main, menu, nav, section, summary{display: block;}
audio, canvas, progress, video{display: inline-block; /* 1 */}
audio:not([controls]){display: none; height: 0;}
progress{vertical-align: baseline;}
[hidden], template{display: none;}
a{background-color: transparent; -webkit-text-decoration-skip: objects; /* 2 */}
a:active, a:hover{outline: 0; -webkit-tap-highlight-color: transparent;}
abbr[title]{border-bottom: 1px dotted;}
b, strong{font-weight: 600;}
dfn{font-style: italic;}
mark{background: #ff0; color: #000;}
small{font-size: 80%;}
sub, sup{font-size: 75%; line-height: 0; position: relative; vertical-align: baseline;}
sup{top: -0.5em;}
sub{bottom: -0.25em;}
svg:not(:root){overflow: hidden;}
hr{box-sizing: border-box; height: 0; border-bottom: 1px solid #ccc; margin-bottom: 10px; border-top-style: none; border-right-style: none; border-left-style: none;}
pre{overflow: auto;}
pre.debug{font-size: 14px !important;}
code, kbd, pre, samp{font-family: monospace, monospace; font-size: 1em;}
button, input, optgroup, select, textarea{color: inherit; /* 1 */ font: inherit; /* 2 */ margin: 0; /* 3 */ -webkit-appearance: none;}
button{overflow: visible;}
button, select{text-transform: none;}
button, html input[type="button"], input[type="reset"], input[type="submit"]{-webkit-appearance: button; /* 2 */ cursor: pointer; /* 3 */}
button[disabled], html input[disabled]{cursor: default;}
button::-moz-focus-inner, [type="button"]::-moz-focus-inner, [type="reset"]::-moz-focus-inner, [type="submit"]::-moz-focus-inner{border-style: none; padding: 0;}
input{line-height: normal; border-radius: 0; box-shadow: none;}
input[type="checkbox"], input[type="radio"]{box-sizing: border-box; /* 1 */ padding: 0; /* 2 */}
input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button{height: auto;}
[type="search"]::-webkit-search-cancel-button, [type="search"]::-webkit-search-decoration{-webkit-appearance: none;}
input[type=text], input[type=email], input[type=password], input[type=tel], input[type=search]{-webkit-appearance: none;}
input[type=number] {-moz-appearance:textfield; -webkit-appearance: textfield; -ms-appearance: textfield; -webkit-appearance: none;}
input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {-webkit-appearance: none;}
::-webkit-input-placeholder{color: inherit; opacity: 1;}
fieldset{border: none; margin: 0; padding: 0;}
textarea{overflow: auto; resize: vertical;}
optgroup{font-weight: 600;}
table{border-collapse: collapse; border-spacing: 0;}
input:-webkit-autofill, textarea:-webkit-autofill, select:-webkit-autofill{-webkit-box-shadow: 0 0 0 30px #fff inset; box-shadow: 0 0 0 30px #fff inset;} /* set browser autocomplete bg yellow color to white */
/*------- /normalize -------*/

@import "_vars.less";
@import "_mixins.less";

/*------- fonts -------*/
@font-face {
	font-family: 'inter';
	src: url('assets/fonts/Inter-Regular.woff2') format('woff2');
	font-weight: 400;
	font-style: normal;
	font-display: block;
}
@font-face {
	font-family: 'inter';
	src: url('assets/fonts/Inter-SemiBold.woff2') format('woff2');
	font-weight: bold;
	font-style: normal;
	font-display: block;
}
@font-face {
	font-family: 'icomoon';
	src: url('assets/fonts/icomoon.woff?v1') format('woff');
	font-weight: normal;
	font-style: normal;
	font-display: block;
}

/*------- /fonts -------*/

/*------- vars -------*/
:root{
	// layout
	--pageWidth: 1440px;
	--pageWidth2: 950px;
	--borderRadius: 12px;
	--elementGap: 24px;
	--wrapperMargin: auto;
	
	// homepage
	--widgetSpacing: 95px;

	// colors
	--textColor: #101117;
	--blue: #0050A0;
	--blueDark: #002D73;

	--turquoise: #10DFBA;
	--red: #C1292E;
	--aqua: #088696;
	--yellow: #FFC857;
	--orange: #E07842;
	--orangeBang: #F65F04;
	--green: #1FB549;

	--white: #ffffff;
	--gray: #F3F3F3;
	--gray2: #D9D9D9;
	--gray3: #F1F1F4;
	--gray4: #C7C7CD;
	--gray5: #818181;
	--gray6: #B6B6B6;
	--placeholderColor: #C7C7CD;
	--black: #000000;
	--red: #DA0D00;

	--successColor: #3ABA5E;
	--errorColor: var(--red);

	// text
	--font: "inter", sans-serif;
	--fonti: "icomoon";
	--fontSizeH1: clamp(30px, 3vw, 42px);
	--fontSizeH2: 28px;
	--fontSizeH3: 24px;
	--fontSizeH4: 18px;
	--fontSize: clamp(15px, 1.5vw, 16px);
	--lineHeight: 1.4;

	@media (max-width: @l){
		--pageWidth: auto;
		--wrapperMargin: 32px;
	}

	@media (max-width: @m){
		--elementGap: 12px;
		--wrapperMargin: 12px;
	}
}
/*------- /vars -------*/

/*------- selectors -------*/
body{background: #fff; font: var(--fontSize)/var(--lineHeight) var(--font); color: var(--textColor);}
ul, ol{margin: 0; padding: 0;}
ol{margin-left: 16px;}
h1, h2, h3, h4{
	padding: 30px 0 0; font-family: var(--font); line-height: 1.2; font-weight: normal; position: relative;
	a, a:hover{text-decoration: none; color: var(--textColor);}
	strong{font-weight: bold;}
}
h1{font-size: var(--fontSizeH1); padding: 0 0 30px;}
h2{font-size: var(--fontSizeH2); padding-bottom: 26px;}
h3{font-size: var(--fontSizeH3); padding-bottom: 22px;}
h4{font-size: var(--fontSizeH4); padding-bottom: 20px;}
p{padding-bottom: 20px;}
a{
	color: var(--blueDark); .transition(color);
	&:hover{color: var(--blueDark); text-decoration: underline;}
}
/*------- /selectors -------*/

/*------- buttons -------*/
@keyframes load {
	0%,80%,100%{transform: scale(0);}
	40%{transform: scale(1);}
}

.btn, input[type=submit], button{
	display: inline-flex; align-items: center; cursor: pointer; justify-content: center; gap: 10px; height: 48px; padding: 0 42px; background: var(--blueDark); border-radius: 100px; font-size: 15px; line-height: 1.4; font-weight: 600; color: var(--white); text-decoration: none; position: relative; transition: background 0.3s, color 0.3s; 
	&.small-radius{border-radius: 8px;}
	@media (min-width: @t){
		&:hover{background: var(--turquoise); color: var(--blueDark); text-decoration: none;}
	}

	@media (max-width: @m){height: 44px; padding: 0 24px;}
}
.btn-sec{
	background: var(--turquoise); color: var(--blueDark);
	@media (min-width: @t){
		&:hover{background: var(--blueDark); color: var(--white);}
	}
}
.btn-outline{
	background: var(--white); color: var(--blueDark); border: 1px solid var(--blueDark);
	@media (min-width: @t){
		&:hover{background: var(--blueDark); color: var(--white);}
	}
}
.btn-disabled{pointer-events: none; background: var(--gray2); color: #fff;}
.btn-icon{
	&:before{.icon-arrow-right2(); font: 14px/1 var(--fonti); .scaleX(-1);}
}
/*------- /buttons -------*/

/*------- tables -------*/
.table{
	width: 100%; border-spacing: 0; margin: 10px 0px 20px;
	th{font-weight: 600; font-size: 14px; text-align: left; padding: 10px 0; border-bottom: 1px solid var(--gray5);}
	td{border-bottom: 1px solid var(--gray6); padding: 10px 10px 10px 0;}
	&.stripe tbody tr:nth-child(even){background: #E9E9E9;}
}
.table-row{display: table; width: 100%;}
.table-col{display: table-cell;}
.table-wrapper{
	overflow-x: scroll; -webkit-overflow-scrolling:touch; width: 100%; position: relative; padding-bottom: 35px;
	&:before{.pseudo(30px,28px); background-size: cover; bottom: 0; right: 4px;}
	&.ios{
		padding-bottom: 40px;
		&:before{.pseudo(30px,28px); background-size: cover; bottom: 0; right: 4px;}
	}
}
/*------- /tables -------*/

/*------- forms -------*/
label{padding: 0 0 4px 0; display: inline-block;}
select{-moz-appearance: none; -o-appearance:none; -webkit-appearance: none; -ms-appearance: none;}
select::-ms-expand {display: none;}
input, textarea, select{width: 100%; height: 48px; padding: 0 12px; background-color: var(--white); border: 1px solid var(--gray5); border-radius: 8px; font-size: 16px; color: var(--gray5); .transition(border-color); -webkit-appearance: none;}
input:disabled, textarea:disabled, input:disabled+label, .disabled{cursor: not-allowed !important; color: #ccc;}
input:hover, textarea:hover, select:hover, input:focus, textarea:focus, select:focus{border-color: var(--inputBorderHover); outline: 0;}
input[type=submit], button{border: none; display: inline-block;}
input[type=checkbox], input[type=radio]{padding: 0; height: auto; border: none;}
textarea{height: 130px; padding-top: 10px; padding-bottom: 10px; line-height: 19px; min-height: 48px;}
legend{
	font-size: 16px; line-height: 18px; font-weight: bold;
	a{text-decoration: none;}
}

input[type=checkbox], input[type=radio]{position: absolute; left: -9999px; display: inline;}
input[type=checkbox] + label, input[type=radio] + label{padding: 0 0 0 32px; text-align: left; cursor: pointer; position: relative;}
input[type=radio] + label{width: 100%;}

input[type=checkbox] + label:before{.pseudo(16px, 16px); background: var(--white); border: 2px solid var(--black); border-radius: 3px; color: #fff; .icon-check(); font: 16px/18px var(--fonti); font-weight: 600; text-align: center; top: 0; left: 0; .transition(all);}
input[type=radio] + label:before{.pseudo(16px, 16px); background: var(--white); border: 2px solid var(--black); border-radius: 200px; color: var(--white); text-align: center; left: 0; top: 0; .transition(all);}
input[type=checkbox]:checked + label:before{background: var(--blueDark); border-color: var(--blueDark); color: var(--white);}
input[type=radio]:checked + label:before{background: var(--blueDark); border-color: var(--blueDark); box-shadow: inset 0px 0px 0px 3px var(--white);}

.form-animated-label{
	p, .field{position: relative;padding-bottom: 10px;}
	.hidden{max-width: 0; max-height: 0; margin: 0; padding: 0; visibility: hidden;}
	label{position: absolute; top: 13px; left: 12px; padding: 0; color: var(--gray5); cursor: text; z-index: 10; .transition(all);}
	.global-error{width: 100%;}
	.focus,.ffl-floated,.floating-label{
		label{top: 7px; font-size: 12px; color: var(--blueDark);}
		input,select{padding-top: 15px; font-size: 16px;}
		textarea{padding-top: 25px;}
	}
	input[type=checkbox] + label, input[type=radio] + label{padding: 0 0 0 32px; text-align: left; cursor: pointer; position: relative; left: unset; top: unset;}
	input[type=radio] + label{font-size: 12px;}

}
/*------- /forms -------*/

/*------- info messages -------*/
.error{
	color: var(--errorColor); display: block; padding: 4px 0 0 16px; font-size: 12px; line-height: 13px;
	@media (max-width: @m){font-size: 11px;}
}
/*
.global-error, .global-success, .global-warning{
	display: flex; align-items: center; font-size: 14px; font-weight: bold; margin: 0 0 15px 0; line-height: 1.4; padding: 10px 18px 10px 48px; min-height: 40px; background: var(--colorRed); color: var(--colorWhite); position: relative; border-radius: var(--borderRadius);
	&:before{.icon-alert(); font: 20px/1 var(--fonti); color: var(--colorWhite); position: absolute; top: 10px; left: 16px;}
}
.global-success{
	background-color: var(--successColor);
	&:before{.icon-success(); font-size: 24px; top: 8px;}
}
.global-warning{
	background-color: var(--warningColor);
	&:before{.icon-info(); font-size: 24px; top: 8px;}
}
*/
/*------- /info messages -------*/

/*------- helpers -------*/
.wrapper{max-width: var(--pageWidth); margin: 0 var(--wrapperMargin);}
.wrapper2{
	max-width: var(--pageWidth2); margin: auto;
	@media (max-width: @m){max-width: 100%;}
}
.line-through{text-decoration: line-through;}
.red{color: var(--red);}
.posr{position: relative;}
.p-w{display: inline-flex; letter-spacing: -0.5px; line-height: 1;}
.p-sep{display: none;}
.p-d{font-size: 60%; padding: 0 6px 0 2px; margin-top: -0.5px;}
.cms-content{
	ol{
		padding: 0 0 20px 20px;
		li{padding-bottom: 2px;}
	}
	ul{
		padding: 0; margin: 0 0 20px 25px;
		li{padding-bottom: 2px;}
	}
}
.blue{color: var(--blueDark);}
.toggle-icon{
	width: 15px; height: 15px; display: flex; align-items: center; justify-content: center; position: relative;
	&:before{.icon-arrow-down(); font: 10px/1 var(--fonti); color: var(--gray4);}
	&.active{
		&:before{.scaleY(-1);}
	}
}
.page-content{padding-bottom: clamp(30px, 4vw, 60px);}
.cards-wrapper{
	display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px;
	@media (max-width: @m){grid-template-columns: repeat(1, 1fr); gap: var(--wrapperMargin);}
}
.items-loading{
	position: relative;
	&:before{.pseudo(auto,auto); inset: 0; opacity: 1; z-index: 11; background: var(--gray3); opacity: 0.6; z-index: 50;}
}
/*------- /helpers -------*/

/*------- swiper -------*/
.swiper-navigation{position: absolute; top: 0; bottom: 0; left: 0; right: 0;}
.swiper-button{
	display: flex; align-items: center; justify-content: center; width: 62px; height: 62px; background: rgba(210, 210, 215, 0.7); border-radius: 100%; font-size: 0; line-height: 0; position: absolute; left: 65px; top: 50%; transform: translateY(-50%); z-index: 1; cursor: pointer; .transition(background);
	&:before{.icon-arrow-down(); margin-right: 4px; font: 20px/1 var(--fonti); color: var(--black); position: absolute; z-index: 1; .rotate(90deg); .transition(color);}
	@media (min-width: @t){
		&:hover{background: rgba(154, 154, 154, 0.8);}
	}
	@media (max-width: @t){left: 25px;}
	@media (max-width: @m){display: none;}
}
.swiper-button-next{
	left: unset; right: 65px; transform: translateY(-50%);
	&:before{margin: 0 0 0 4px; .rotate(-90deg);}

	@media (max-width: @t){right: 25px;}
}
.swiper-button-disabled{opacity: 0; cursor: default;}
.swiper-button-lock, .swiper-pagination-lock{display: none;}
.swiper-scrollbar-wrapper{position: relative; margin-top: 20px;}
.swiper-scrollbar{
	width: 100%; height: 6px; background: var(--gray2); border-radius: 4px;
	@media (max-width: @m){height: 3px;}
}
.swiper-scrollbar-drag{
	height: 6px; background: var(--blue); border-radius: 4px;
	@media (max-width: @m){height: 3px;}
}
/*------- /swiper -------*/

/*------- global -------*/
body{
	background: var(--gray3);
	&.o-active{overflow: hidden;}

	@media (max-width: @m){
		&.nav-categories-active{
			overflow: hidden;
			.sw{display: none;}
		}
	}
}
.page-wrapper{overflow-x: hidden;}
.c-items{
	display: grid; grid-template-columns: repeat(auto-fill, minmax(270px, 1fr)); gap: 18px;
	@media (max-width: @m){grid-template-columns: repeat(2, 1fr); gap: 8px;}
	&.list{
		gap: var(--elementGap); grid-template-columns: 1fr;
	}

}
/*------- /global -------*/