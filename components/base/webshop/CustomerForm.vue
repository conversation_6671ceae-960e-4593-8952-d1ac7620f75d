<template>
	<BaseForm @submit="onSubmit" :loading="loading" v-slot="{errors, meta, values}" v-bind="$attrs">
		<slot :loading="loading" :errors="errors" :meta="meta" :values="values" :fields="fields" :status="status" />
	</BaseForm>
</template>

<script setup>
	const emit = defineEmits(['load', 'submit']);
	const config = useAppConfig();
	const auth = useAuth();
	const webshop = useWebshop();
	const {getAppUrl} = useApiRoutes();
	const dom = useDom();
	const labels = useLabels();
	const props = defineProps({
		submitUrl: {
			type: String,
			default: 'webshop_shipping',
		},
		changeFields: String,
	});
	const status = ref(null);
	const loading = ref(false);

	// fetch form fields
	const fields = ref(null);

	// change fields types and validation if needed: https://hapi.marker.hr/#/Customer/get_v1_customer_fields_
	const changeFields = {};
	if (props.changeFields) {
		changeFields.change_fields = props.changeFields;
	}

	onMounted(async () => {
		fields.value = await auth.fetchForm({type: 'webshop.customer', ...changeFields}).then(res => res.data);

		// wait for next tick (DOM update) to make sure form is rendered
		await nextTick();

		// wrap r1 and company_oib fields
		wrapFields('field-group field-group-r1', ['b_r1', 'b_company_oib', 'b_company_name', 'b_company_address', 'b_company_zipcode', 'b_company_city']);

		// wrap b_shipping fields
		wrapFields('field-group field-group-shipping', ['b_same_as_shipping', 'b_first_name', 'b_last_name', 'b_address', 'b_zipcode', 'b_city', 'b_location', 'b_phone']);

		// set focus on first field in form. "data-autofocus" attribute needs to be set on form element
		dom.setFieldFocus();

		// emit event when form is loaded. Can be used to trigger analytics event or similar
		emit('load');
	});

	async function onSubmit({values, actions}) {
		loading.value = true;
		const formValues = values;
		if (props.changeFields) {
			formValues._change_fields = props.changeFields;
		}

		const res = await webshop.submitCustomerData(formValues);
		await webshop.fetchCustomer();
		status.value = res;

		// set field errors if response contains api errors
		if (res?.data?.errors?.length) {
			loading.value = false;
			res.data.errors.forEach(error => {
				actions.setFieldError(error.field, labels.get(error.error, error.error));
			});
			return res;
		}

		emit('submit', res);

		// if no api errors, navigate to next step
		if (res?.success && !res.data?.errors?.length && props.submitUrl) {
			return navigateTo(getAppUrl(props.submitUrl));
		}
		loading.value = false;
	}

	// wrap related fields with subfields
	function wrapFields(cssClass, relatedFields) {
		const rootField = document.getElementById(relatedFields[0]);
		if (!rootField) return;

		// get root element
		const rootElement = rootField.parentElement.parentElement;

		// create wrapper div
		let wrapperDiv = document.createElement('div');
		wrapperDiv.className = cssClass;

		// Move elements to the wrapper
		const relatedField = document.getElementById(relatedFields[0]).parentElement;
		rootElement.insertBefore(wrapperDiv, relatedField);
		relatedFields.forEach(field => {
			const el = document.querySelector(`input[name="${field}"]`);
			if (el) wrapperDiv.appendChild(el.parentElement);

			// add event listener to main field
			if (field == relatedFields[0]) {
				el.addEventListener('change', function () {
					wrapperDiv.classList.toggle('active');
				});

				// set active class on load if checkbox is checked
				if (el.name == 'b_r1' && el.checked) wrapperDiv.classList.add('active');
				if (el.name == 'b_same_as_shipping' && !el.checked) wrapperDiv.classList.add('active');
			}
		});
	}
</script>

<style>
	.field-group > *:not(:first-child) {
		display: none;
	}
	.field-group.active > *:not(:first-child) {
		display: block;
	}
</style>
