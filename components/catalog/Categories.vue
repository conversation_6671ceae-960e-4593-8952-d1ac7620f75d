<template>
	<!-- FIXME INTEG promoijeniti u adminu rotator code => categories -->
	<BaseCmsRotator :fetch="{code: 'homepage_categories', limit: 16, response_fields: ['id','element_title_small', 'element_color_1', 'element_color_4', 'link_target_blank','url_without_domain','image_upload_path','image_thumbs','title']}" thumb-preset="categories" v-slot="{items}">
		<div v-if="items?.length" class="categories">
			<CmsCardSmall v-for="item in items" :key="item.id" :href="item.url_without_domain">
				<template #header>
					<span v-if="item.element_title_small" class="category-badge" :style="{backgroundColor: item.element_color_4 ? item.element_color_4 : '#DA0D00', color: item.element_color_1 ? item.element_color_1 : '#fff'}" v-html="item.element_title_small"></span>
					<span class="img">
						<BaseUiImage :data="item.image_upload_path_thumb" default="/images/no-image.jpg" />
						<!--<BaseUiImage :data="item.image_thumbs?.['width200-height200']" default="/images/no-image.jpg" />-->
					</span>
				</template>
				<template #content>
					<span class="title">{{ item.title }}</span>
				</template>
			</CmsCardSmall>
		</div>
	</BaseCmsRotator>
</template>

<style lang="less" scoped>
	.categories{
		display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: var(--elementGap); margin-bottom: 65px;
		@media (max-width: @ms) {
			display: flex; gap: 10px; margin: 0 -10px 36px; padding: 0 10px; overflow-y: hidden; overflow-x: auto; position: relative;
			&::-webkit-scrollbar{display: none;}
			&::-webkit-scrollbar-thumb{display: none;}
		}
	}
	.category{
		display: flex; flex-direction: column; align-items: center; padding: 12px; background: var(--white); border-radius: var(--borderRadius); font-size: 15px; line-height: 1.35; font-weight: bold; color: var(--textColor); text-align: center; text-decoration: none; position: relative;
		.title{display: flex; align-items: center; height: 100%;}
		.img{display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 120px; margin-bottom: 12px; aspect-ratio: 1;}
		:deep(img){display: block; width: auto; height: auto; max-width: 100%; max-height: 100%;}

		@media (max-width: @ms){
			border-radius: 7px; font-size: 14px; padding: 10px; line-height: 1.3; flex: 0 0 calc(100% / 3.5);
			.img{width: 74px; margin-bottom: 6px;}
		}
	}
	.category-badge{
		display: flex; align-items: center; justify-content: center; min-height: 22px; padding: 0 10px; background: var(--errorColor); border-radius: 4px; font-size: 12px; font-weight: 600; text-align: center; color: var(--white); position: absolute; top: 15px; right: -12px; z-index: 1;
	
		@media (max-width: @m){top: 12px; right: -10px;}
	}
</style>