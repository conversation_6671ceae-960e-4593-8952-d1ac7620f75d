<template>
	<div class="section-header">
		<div class="section-title"><slot /></div>
		<div class="section-btn"><slot name="btn" /></div>
	</div>
</template>

<style scoped lang="less">
	.section-header{
		display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; position: relative; z-index: 1;

		@media (max-width: @m){margin-bottom: 12px;}
	}
	.section-title{
		flex-grow: 1; margin-right: 30px; font-size: 28px; font-weight: 600; line-height: 1.2; letter-spacing: -0.28px;

		@media (max-width: @m){font-size: 20px; line-height: 1.3; letter-spacing: 0;}
	}
	.section-btn{
		:deep(a){
			display: inline-flex; align-items: center; flex-shrink: 0; padding-right: 30px; font-size: 16px; line-height: 1.5; font-weight: 600; text-decoration: none; color: var(--textColor); position: relative;
			&:before{.icon-arrow-right(); font: 17px/1 var(--fonti); color: var(--textColor); position: absolute; right: 0;}

			@media (max-width: @m){
				padding-right: 24px; font-size: 14px; line-height: 1.4;
				&:before{font-size: 14px;}
			}
		}
	}	
</style>